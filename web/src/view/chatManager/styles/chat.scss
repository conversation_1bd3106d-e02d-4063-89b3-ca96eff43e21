// 聊天相关样式
.chat-dialog {
  :deep(.el-dialog) {
    margin: 5vh auto;
    height: 600px !important;
    max-height: 750px;
    border-radius: 12px;
    overflow: hidden;
    background: #1f2937;
    border: 1px solid #374151;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    padding: 0 !important;
  }

  :deep(.el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    // height: calc(750px - 60px);
  }
}

// 会话卡片样式
.conversation-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &.active {
    background-color: rgba(52, 152, 219, 0.3);
  }
}

// 消息气泡样式
.message-bubble {
  max-width: 70%;
  word-wrap: break-word;
  border-radius: 12px;
  position: relative;

  &.own-bubble {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    margin-left: auto;
    border-radius: 12px;

    &::after {
      content: '';
      position: absolute;
      right: -6px;
      top: 10px;
      width: 0;
      height: 0;
      border-left: 6px solid #22c55e;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }

  &.other-bubble {
    background: #374151;
    color: #f3f4f6;
    border: 1px solid #4b5563;
    border-radius: 12px;

    &::before {
      content: '';
      position: absolute;
      left: -6px;
      top: 10px;
      width: 0;
      height: 0;
      border-right: 6px solid #374151;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }
  }
}

// 在线状态点
.online-dot {
  width: 12px;
  height: 12px;
  background: #27ae60;
  border: 2px solid #2c3e50;
  border-radius: 50%;
  position: absolute;
  bottom: 2px;
  right: 2px;
}

// 滚动条样式
.el-scrollbar__bar {
  &.is-vertical {
    right: 2px;
    width: 6px;
    
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
    }
  }
}

// 聊天容器样式
.chat-container {
  height: 100%;
  background: #1f2937;
  border-radius: 12px;
  overflow: hidden;
}

// 会话侧边栏样式
.conversation-sidebar {
  background: #111827;
  border-radius: 12px 0 0 12px;
  border-right: 1px solid #374151;
}

// 聊天主体区域样式
.chat-main {
  background: #374151;
  border-radius: 0;
  height: 100%;
}

// 群成员面板样式
.group-members {
  width: 240px !important; // 固定宽度240px
  background: #1f2937;
  border-radius: 0 12px 12px 0;
  border-left: 1px solid #4b5563;
}

// 响应式设计
@media (max-width: 768px) {
  .chat-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 2.5vh auto !important;
      height: 750px !important;
    }
  }

  .conversation-sidebar {
    width: 240px !important;
  }

  .group-members {
    width: 240px !important;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-item {
  animation: fadeIn 0.3s ease-out;
}

// 表情选择器样式
.emoji-picker {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #4b5563;
  border-radius: 8px;
  background: #374151;

  .emoji-item {
    transition: all 0.2s ease;

    &:hover {
      background: #4b5563;
      transform: scale(1.1);
    }
  }
}

// 移除白色背景和边框
.el-empty {
  background: transparent !important;
  border: none !important;
}

// 确保所有深色区域有圆角
.conversation-sidebar,
.chat-main,
.group-members {
  border-radius: 8px;
}

// 消息输入框样式
.message-input {
  background: #4b5563;
  border-top: 1px solid #6b7280;
  border-radius: 0 0 8px 8px;
}

// 头部样式
.dialog-header {
  background: #1f2937;
  border-radius: 8px 8px 0 0;
  padding: 16px 20px;
  border-bottom: 1px solid #374151;
}
