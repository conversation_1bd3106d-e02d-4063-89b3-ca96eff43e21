import './style/element_visiable.scss'
import 'element-plus/theme-chalk/dark/css-vars.css'
import { createApp } from 'vue'
import ElementPlus from 'element-plus'

import 'element-plus/dist/index.css'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import { store } from '@/pinia'
import App from './App.vue'
import { initDatabase } from '@/utils/db.js'
// 引入调试工具（开发环境）
if (process.env.NODE_ENV === 'development') {
  import('@/utils/dbDebug.js')
}

const app = createApp(App)
app.config.productionTip = false

// 初始化IndexedDB数据库
initDatabase().then(success => {
  if (success) {
    console.log('✅ IndexedDB数据库初始化成功')
  } else {
    console.error('❌ IndexedDB数据库初始化失败')
  }
}).catch(error => {
  console.error('❌ IndexedDB数据库初始化异常:', error)
})

app.use(run).use(ElementPlus).use(store).use(auth).use(router).mount('#app')
export default app
